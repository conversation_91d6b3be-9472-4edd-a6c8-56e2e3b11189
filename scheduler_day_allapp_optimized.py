import argparse
import os
import subprocess
import time
import multiprocessing as mp
from datetime import datetime, timedel<PERSON>

def run_task_optimized(session_cgi_label: str, use_multiprocess: bool = True, max_workers: int = None, chunk_size: int = 1000):
    """优化版本的任务执行函数"""
    try:
        # Step 1: 根据时间规则生成文件名
        print(f"[{datetime.now()}] Step 1: Determining the generated file name...")
        yesterday = datetime.now() - timedelta(days=1)
        start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)

        start_str = start_time.strftime('%Y%m%d%H')  # 格式化为 YYYYMMDDHH
        end_str = end_time.strftime('%Y%m%d%H')      # 格式化为 YYYYMMDDHH

        # 动态生成文件名
        generated_file = f"data_{start_str}_{end_str}_all_app_{session_cgi_label}_new.pickle"
        print(f"[{datetime.now()}] To be Generated file name: {generated_file}")

        # Step 2: 执行优化版本的 xml_decode_b_understand_all_app.py
        print(f"[{datetime.now()}] Step 2: Running optimized xml_decode_b_understand_all_app.py...")
        
        # 构建命令参数
        cmd_args = [
            "python", "xml_decode_b_understand_all_app.py", 
            f"--output_file_name={generated_file}", 
            f"--start_time={start_time.strftime('%Y-%m-%d %H:%M:%S')}", 
            f"--end_time={end_time.strftime('%Y-%m-%d %H:%M:%S')}", 
            f"--session_cgi_label={session_cgi_label}",
            f"--use_multiprocess={use_multiprocess}",
            f"--chunk_size={chunk_size}"
        ]
        
        if max_workers:
            cmd_args.append(f"--max_workers={max_workers}")
        
        print(f"[{datetime.now()}] 执行命令: {' '.join(cmd_args)}")
        
        result_step1 = subprocess.run(
            cmd_args,
            check=True,
            capture_output=True,
            text=True
        )
        print(f"[{datetime.now()}] Step 2 completed successfully.")
        print(result_step1.stdout)

        # 检查文件是否存在
        if not os.path.exists(generated_file):
            raise FileNotFoundError(f"Generated file '{generated_file}' not found!")
        
        # Step 2.5: 将生成的文件复制到目标目录
        subprocess.run(
            ["cp", generated_file, "/mnt/cephfs/user_claireysun/data/xcx_pickle/"],
            check=True
        )
        print(f"[{datetime.now()}] Step 2.5 completed successfully. File copied to destination.")

        # Step 3: 执行 group_by_session_id.py
        print(f"[{datetime.now()}] Step 3: Running group_by_session_id.py with input file '{generated_file}'...")
        output_json_path = f"{generated_file.split('.')[0]}_groupby_session.json"
        result_step3 = subprocess.run(
            [
                "python", "group_by_session_id.py",
                "--function=group_by_session_id",
                "--input_pickle_dir=./",
                f"--target_pickles={generated_file}",
                "--trajectory_length_min=1",
                "--trajectory_length_max=20",
                f"--output_json_path={output_json_path}"
            ],
            check=True,
            capture_output=True,
            text=True
        )
        print(f"[{datetime.now()}] Step 3 completed successfully.")
        print(result_step3.stdout)

        # Step 4: 将 output_json_path 文件复制到 /mnt/ce
        print(f"[{datetime.now()}] Step 4: Copying '{output_json_path}' to '/mnt/cephfs/user_claireysun/data/xcx_session/'...")
        if not os.path.exists(output_json_path):
            raise FileNotFoundError(f"Output JSON file '{output_json_path}' not found!")
        subprocess.run(
            ["cp", output_json_path, "/mnt/cephfs/user_claireysun/data/xcx_session/"],
            check=True
        )
        print(f"[{datetime.now()}] Step 4 completed successfully. File copied to destination.")

        # Step 5: 删除生成的文件
        print(f"[{datetime.now()}] Step 5: Cleaning up generated files...")
        if os.path.exists(generated_file):
            os.remove(generated_file)
            print(f"[{datetime.now()}] Deleted file: {generated_file}")
        if os.path.exists(output_json_path):
            os.remove(output_json_path)
            print(f"[{datetime.now()}] Deleted file: {output_json_path}")

        print(f"[{datetime.now()}] 任务完成！")

    except subprocess.CalledProcessError as e:
        print(f"[{datetime.now()}] Error occurred while running a subprocess:")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
    except FileNotFoundError as e:
        print(f"[{datetime.now()}] Error: {e}")
    except Exception as e:
        print(f"[{datetime.now()}] Unexpected error: {e}")

def run_task(session_cgi_label: str):
    """原始版本的任务执行函数（保留兼容性）"""
    run_task_optimized(session_cgi_label, use_multiprocess=False)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="优化版定时任务脚本")
    parser.add_argument("--interval", type=int, default=60, help="定时任务间隔时间（秒）")
    parser.add_argument("--start_time", type=str, default="00:00", help="定时任务开始时间（格式：HH:MM）")
    parser.add_argument("--end_time", type=str, default="00:10", help="定时任务结束时间（格式：HH:MM）")
    parser.add_argument("--session_cgi_label", type=str, default="cgi7", help="会话 CGI 标签")
    parser.add_argument("--use_multiprocess", action="store_true", help="是否使用多进程加速")
    parser.add_argument("--max_workers", type=int, default=None, help="最大进程数")
    parser.add_argument("--chunk_size", type=int, default=1000, help="每个批次处理的行数")
    parser.add_argument("--run_once", action="store_true", help="只运行一次，不进入定时循环")
    
    args = parser.parse_args()
    
    print("优化版调度器启动")
    print(f"配置参数:")
    print(f"  会话标签: {args.session_cgi_label}")
    print(f"  多进程模式: {args.use_multiprocess}")
    print(f"  最大进程数: {args.max_workers or '自动'}")
    print(f"  批次大小: {args.chunk_size}")
    print(f"  系统CPU核心数: {mp.cpu_count()}")
    
    if args.run_once:
        print("单次运行模式")
        run_task_optimized(
            args.session_cgi_label, 
            args.use_multiprocess, 
            args.max_workers, 
            args.chunk_size
        )
    else:
        print(f"定时任务模式: {args.start_time} - {args.end_time}")
        # 定时任务主循环
        while True:
            # 获取当前时间
            current_time = datetime.now().strftime("%H:%M")
            
            # 检查是否是目标时间范围
            if args.start_time <= current_time <= args.end_time:
                run_task_optimized(
                    args.session_cgi_label, 
                    args.use_multiprocess, 
                    args.max_workers, 
                    args.chunk_size
                )
                time.sleep(3600)  # 等待一个小时，避免重复执行

            # 每分钟检查一次时间
            time.sleep(args.interval)
